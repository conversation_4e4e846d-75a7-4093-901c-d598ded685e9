'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  UserPlus,
  Edit,
  Trash2,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Upload,
  Key,
  Clock,
  AlertCircle,
  Users,
  Eye,
  Trash,
} from 'lucide-react';
import ResetPasswordModal from '@/components/admin/ResetPasswordModal';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuPortal,
} from '@/components/ui/dropdown-menu';
import { UserAvatar } from '@/components/UserAvatar';
import { useUsers, UserFilters as UserFilterParams } from '@/hooks/useAdminData';
import { toast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import DeleteUserDialog from '@/components/admin/DeleteUserDialog';
import UserFormModal from '@/components/admin/UserFormModal';
import ImportUsersModal from '@/components/admin/ImportUsersModal';
import UserBulkActions from '@/components/admin/UserBulkActions';
import UserFilters, { UserFilterOptions } from '@/components/admin/UserFilters';
import { Checkbox } from '@/components/ui/checkbox';
import Pagination from '@/components/admin/Pagination';

// Define User interface
interface User {
  id: string;
  name: string;
  email: string;
  role: 'user' | 'admin' | 'moderator' | 'superadmin';
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  verified: boolean;
  profileImage?: string;
  picture?: string; // Added picture field explicitly
  createdAt: string;
  lastLogin?: string | null; // Always expect ISO string from API
  subscription?: string | { plan: string; status: string };
  emailVerified?: Date | null;
  // For any additional properties with specific types
  [key: string]: string | number | boolean | Date | null | undefined | { [key: string]: string };
}

// Define EditUserData interface to match what UserFormModal expects
interface EditUserData {
  id: string;
  name: string;
  email: string;
  role: string;
  emailVerified?: Date | null;
}

export default function UsersPage() {
  // We don't need to check auth here as it's handled by middleware
  const router = useRouter();
  const { user: currentUser } = useAuth();

  // Admin access is now protected by middleware and layout
  // State for search, filters and pagination
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [roleFilter, setRoleFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [advancedFilters, setAdvancedFilters] = useState<UserFilterOptions>({
    search: '',
    role: [],
    status: [],
    verified: null,
    dateRange: {
      from: null,
      to: null
    },
    subscription: []
  });

  // State for modals
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [isResetPasswordModalOpen, setIsResetPasswordModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<EditUserData | undefined>(undefined);
  const [selectedUserForPasswordReset, setSelectedUserForPasswordReset] = useState<{id: string, name: string} | null>(null);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  // Ref for export link
  const exportLinkRef = useRef<HTMLAnchorElement>(null);

  // State for bulk selection
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Create filters object for useUsers hook
  const filters: UserFilterParams = {
    page: currentPage,
    limit: 10,
    search: searchQuery,
    role: roleFilter,
    status: statusFilter,
    dateFrom: advancedFilters.dateRange.from ? advancedFilters.dateRange.from.toISOString() : null,
    dateTo: advancedFilters.dateRange.to ? advancedFilters.dateRange.to.toISOString() : null,
    subscription: advancedFilters.subscription.length === 1 ? advancedFilters.subscription[0] : '',
    verified: advancedFilters.verified
  };

  // State for loading
  const [isLoadingAction, setIsLoadingAction] = useState(false);

  // Fetch users data
  const {
    data: userData,
    isLoading,
    error,
    refetch
  } = useUsers(filters);

  // Extract users and pagination info with proper defaults
  const users = userData?.users || [];
  const pagination = userData?.pagination || { page: 1, limit: 10, total: 0, pages: 1 };

  // Debug user data with more details
  console.log('Admin users data:', users.map(user => ({
    id: user.id,
    name: user.name,
    email: user.email,
    profileImage: user.profileImage,
    picture: user.picture,
    hasProfileImage: !!user.profileImage,
    hasPicture: !!user.picture
  })));

  // Update filters when search query changes
  useEffect(() => {
    setAdvancedFilters(prev => ({
      ...prev,
      search: searchQuery
    }));
  }, [searchQuery]);

  // Handle filter changes
  const handleFilterChange = (filters: UserFilterOptions) => {
    setAdvancedFilters(filters);

    // Update the search query to match the filter search
    if (searchQuery !== filters.search) {
      setSearchQuery(filters.search);
    }

    // Update role filter based on advanced filters
    if (filters.role.length === 1) {
      setRoleFilter(filters.role[0]);
    } else {
      setRoleFilter('');
    }

    // Update status filter based on verified status
    if (filters.verified !== null) {
      setStatusFilter(filters.verified ? 'verified' : 'unverified');
    } else {
      setStatusFilter('');
    }

    // Reset to first page when filters change
    setCurrentPage(1);

    // Refetch with new filters
    refetch();
  };

  // Reset filters
  const handleResetFilters = () => {
    setSearchQuery('');
    setRoleFilter('');
    setStatusFilter('');
    setAdvancedFilters({
      search: '',
      role: [],
      status: [],
      verified: null,
      dateRange: {
        from: null,
        to: null
      },
      subscription: []
    });
    refetch();
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Trigger refetch with new page
    setTimeout(() => refetch(), 0);
  };

  // Format date for display
  const formatDate = (dateInput: string | Date | undefined | null) => {
    // Handle null, undefined, or empty string
    if (dateInput === null || dateInput === undefined || dateInput === '') {
      return 'N/A';
    }

    try {
      // Handle string or Date object
      const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'N/A';
      }

      // Check if date is in the future (which would be an error)
      const now = new Date();
      if (date > now) {
        console.warn('Future date detected in formatDate:', date);
        return 'Invalid date';
      }

      // Format the date with time
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }).format(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'N/A';
    }
  };

  // Handle user deletion - open dialog
  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
  };

  // Handle delete confirmation from dialog
  const handleDeleteConfirmed = () => {
    refetch(); // Refresh the user list after successful deletion
    setUserToDelete(null); // Close the dialog
  };

  // Open edit user modal
  const handleEditUser = (user: User) => {
    // Convert User to EditUserData format
    const editUserData: EditUserData = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      emailVerified: user.verified ? new Date() : null
    };
    setEditingUser(editUserData);
    setIsUserModalOpen(true);
  };

  // Handle reset password
  const handleResetPassword = (userId: string, userName: string) => {
    setSelectedUserForPasswordReset({ id: userId, name: userName });
    setIsResetPasswordModalOpen(true);
  };

  // Open add user modal
  const handleAddUser = () => {
    setEditingUser(undefined);
    setIsUserModalOpen(true);
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsUserModalOpen(false);
    setEditingUser(undefined);
  };

  // Handle user selection for bulk actions
  const handleUserSelection = (userId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  // Handle select all users
  const handleSelectAll = (isSelected: boolean) => {
    setSelectAll(isSelected);
    if (isSelected) {
      setSelectedUsers(users.map((user: User) => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  // Handle bulk action completion
  const handleBulkActionComplete = useCallback(() => {
    setSelectedUsers([]);
    setSelectAll(false);
    refetch();
  }, [refetch]);

  // Handle role change
  const handleRoleChange = async (userId: string, newRole: 'user' | 'admin' | 'superadmin') => {
    try {
      setIsLoadingAction(true);

      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ role: newRole }),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update user role (${response.status})`);
      }

      toast({
        title: 'Role updated',
        description: `User role has been updated to ${newRole}.`,
        variant: 'success'
      });

      // Refresh the user list
      refetch();
    } catch (error) {
      console.error('Error updating user role:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update user role',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingAction(false);
    }
  };

  // Handle export users
  const handleExportUsers = async (format: 'json' | 'csv') => {
    try {
      // Create export URL with format parameter
      const exportUrl = `/api/admin/users/export?format=${format}`;

      if (format === 'json') {
        // For JSON, open in a new tab
        window.open(exportUrl, '_blank');
      } else {
        // For CSV, use the hidden download link
        if (exportLinkRef.current) {
          exportLinkRef.current.href = exportUrl;
          exportLinkRef.current.click();
        }
      }

      toast({
        title: 'Export Started',
        description: `Users are being exported as ${format.toUpperCase()}.`,
        variant: 'success'
      });
    } catch (error) {
      console.error('Error exporting users:', error);
      toast({
        title: 'Export Failed',
        description: error instanceof Error ? error.message : 'Failed to export users',
        variant: 'destructive'
      });
    }
  };

  // Handle status change
  const handleStatusChange = async (userId: string, newStatus: 'active' | 'inactive' | 'pending' | 'suspended') => {
    try {
      setIsLoadingAction(true);

      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus }),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update user status (${response.status})`);
      }

      // Refresh the user list
      refetch();

      toast({
        title: 'Status updated',
        description: `User status has been updated to ${newStatus}.`,
        variant: 'success'
      });
    } catch (error) {
      console.error('Error updating user status:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update user status',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingAction(false);
    }
  };

  // Handle refreshing the data
  const handleRefreshData = async () => {
    try {
      setIsLoadingAction(true);

      // Use SWR's built-in cache invalidation and revalidation
      await refetch();

      // Show success message
      toast({
        title: 'Data Refreshed',
        description: 'The user data has been refreshed from the database.',
        variant: 'success'
      });
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast({
        title: 'Error',
        description: 'Failed to refresh data',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingAction(false);
    }
  };

  const ActionDropdown = ({ user }: { user: User }) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="hover:bg-vista-light/10 focus-visible:ring-offset-0 focus-visible:ring-0 rounded-full h-8 w-8 md:h-9 md:w-9 transition-colors duration-200 opacity-70 hover:opacity-100 group-hover:opacity-100"
        >
          <MoreHorizontal className="h-4 w-4 md:h-5 md:w-5 text-vista-light" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        side="bottom"
        className="border border-vista-light/20 bg-vista-dark shadow-xl w-48 md:w-56 z-[100] rounded-lg overflow-hidden max-h-[80vh] overflow-y-auto"
        sideOffset={4}
        collisionPadding={32}
        avoidCollisions={true}
        sticky="partial"
        hideWhenDetached={false}
        alignOffset={-8}
      >
          <DropdownMenuLabel className="text-vista-light/90 text-sm font-medium px-3 py-2">Actions</DropdownMenuLabel>
          <DropdownMenuItem
            onClick={() => handleEditUser(user)}
            className="hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
          >
            <Edit className="mr-2 h-4 w-4 flex-shrink-0" />
            <span>Edit User</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleResetPassword(user.id, user.name)}
            className="hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
          >
            <Key className="mr-2 h-4 w-4 flex-shrink-0" />
            <span>Reset Password</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator className="bg-vista-light/15 my-1" />
          <DropdownMenuLabel className="text-vista-light/90 text-sm font-medium px-3 py-2">Change Role</DropdownMenuLabel>
          <DropdownMenuItem
            onClick={() => handleRoleChange(user.id, 'user')}
            className="hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
          >
            <span className="mr-2 w-4 h-4 flex-shrink-0"></span>
            <span>Make User</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleRoleChange(user.id, 'admin')}
            className="hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
          >
            <Users className="mr-2 h-4 w-4 flex-shrink-0" />
            <span>Make Admin</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleRoleChange(user.id, 'superadmin')}
            className="hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
          >
            <span className="mr-2 w-4 h-4 flex-shrink-0"></span>
            <span>Make Super Admin</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator className="bg-vista-light/15 my-1" />
          <DropdownMenuLabel className="text-vista-light/90 text-sm font-medium px-3 py-2">Status</DropdownMenuLabel>
          <DropdownMenuItem
            onClick={() => handleStatusChange(user.id, 'active')}
            className="hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
          >
            <CheckCircle className="mr-2 h-4 w-4 text-green-500 flex-shrink-0" />
            <span>Set Active</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleStatusChange(user.id, 'inactive')}
            className="hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
          >
            <XCircle className="mr-2 h-4 w-4 text-red-500 flex-shrink-0" />
            <span>Set Inactive</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleStatusChange(user.id, 'pending')}
            className="hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
          >
            <Clock className="mr-2 h-4 w-4 text-yellow-500 flex-shrink-0" />
            <span>Set Pending</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleStatusChange(user.id, 'suspended')}
            className="hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
          >
            <AlertCircle className="mr-2 h-4 w-4 text-orange-500 flex-shrink-0" />
            <span>Set Suspended</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator className="bg-vista-light/15 my-1" />
          <DropdownMenuItem
            className="text-red-300 focus:text-red-300 hover:bg-red-500/20 focus:bg-red-500/20 cursor-pointer py-2 px-3 text-sm transition-colors duration-150"
            onClick={() => handleDeleteUser(user)}
          >
            <Trash2 className="mr-2 h-4 w-4 flex-shrink-0" />
            <span>Delete User</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
    </DropdownMenu>
  )

  return (
    <div className="space-y-3 sm:space-y-4 md:space-y-6">
      {/* Mobile-optimized header */}
      <div className="flex flex-col gap-3 sm:gap-4 mb-4 sm:mb-6 md:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
          <div className="min-w-0 flex-1">
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-vista-light mb-1 flex items-center gap-2">
              <Users className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8 text-vista-blue flex-shrink-0" />
              <span className="truncate">User Management</span>
            </h1>
            <p className="text-vista-light/80 text-sm sm:text-base">
              Manage user accounts, roles, and permissions
            </p>
          </div>

          {/* Mobile-optimized action buttons */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <div className="flex gap-2 sm:gap-3">
              <div className="flex-1 sm:flex-initial">
                <UserBulkActions
                  selectedUsers={selectedUsers}
                  onActionComplete={handleBulkActionComplete}
                />
              </div>
              <Button
                onClick={handleAddUser}
                className="bg-vista-light text-vista-dark hover:bg-vista-light/90 h-9 sm:h-10 md:h-11 px-3 sm:px-4 md:px-5 text-sm sm:text-base font-medium shadow-md relative overflow-hidden group transition-all duration-300 flex-1 sm:flex-initial"
              >
                <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-white/80 to-white/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                <span className="relative flex items-center justify-center">
                  <UserPlus className="mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5 text-vista-blue group-hover:scale-110 transition-transform duration-300 flex-shrink-0" />
                  <span className="hidden sm:inline">Add New User</span>
                  <span className="sm:hidden">Add User</span>
                </span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <Card className="border-vista-light/15 bg-vista-dark/30 shadow-lg">
        <CardHeader className="pb-3 sm:pb-4 border-b border-vista-light/10 px-3 sm:px-6">
          <CardTitle className="text-vista-light text-lg sm:text-xl flex items-center gap-2">
            <div className="h-1.5 w-1.5 rounded-full bg-vista-blue mr-1 flex-shrink-0"></div>
            <span className="truncate">Users</span>
            <span className="text-xs sm:text-sm font-normal bg-vista-blue/20 py-0.5 px-1.5 sm:px-2 rounded-full text-vista-light/90 ml-2 hidden sm:inline">Management Panel</span>
          </CardTitle>
          <CardDescription className="text-sm sm:text-base">
            Total users: <span className="text-vista-blue font-medium">{pagination.total}</span>
          </CardDescription>
        </CardHeader>
        <CardContent className="px-3 sm:px-6">
          {/* Advanced User Filters */}
          <div className="bg-vista-dark/20 p-3 sm:p-4 rounded-md border border-vista-light/5 mb-4 sm:mb-6">
            <UserFilters
              onFilterChange={handleFilterChange}
              onReset={handleResetFilters}
            />
          </div>

          {/* Mobile-optimized Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 mb-4 sm:mb-6">
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 flex-1">
              <Button variant="outline" onClick={handleRefreshData} disabled={isLoading || isLoadingAction}
                className="bg-vista-dark/40 border-vista-light/20 hover:bg-vista-blue/10 hover:border-vista-blue/30 h-9 sm:h-10 px-3 sm:px-4 text-sm font-medium transition-all duration-200 group w-full sm:w-auto">
                <RefreshCw className={`mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5 transition-transform duration-300 ${isLoading || isLoadingAction ? 'animate-spin' : 'group-hover:rotate-180'} flex-shrink-0`} />
                <span className="truncate">{isLoading || isLoadingAction ? 'Loading...' : 'Refresh Data'}</span>
              </Button>

              {/* Export Users Button with Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="bg-vista-dark/40 border-vista-light/20 hover:bg-vista-blue/10 hover:border-vista-blue/30 h-9 sm:h-10 px-3 sm:px-4 text-sm font-medium transition-colors duration-200 w-full sm:w-auto">
                    <Download className="mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                    <span className="truncate">Export Users</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  side="bottom"
                  className="border border-vista-light/20 bg-vista-dark shadow-xl w-48 z-[100] rounded-lg overflow-hidden"
                  sideOffset={4}
                  collisionPadding={32}
                  avoidCollisions={true}
                  sticky="partial"
                  hideWhenDetached={false}
                  alignOffset={-8}
                >
                    <DropdownMenuItem
                      onClick={() => handleExportUsers('json')}
                      className="hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
                    >
                      <span>Export as JSON</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleExportUsers('csv')}
                      className="hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
                    >
                      <span>Export as CSV</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
              </DropdownMenu>

              {/* Hidden download link for export */}
              <a
                ref={exportLinkRef}
                style={{ display: 'none' }}
                download="streamvista_users.csv"
              />

              <Button variant="outline" onClick={() => setIsImportModalOpen(true)}
                className="bg-vista-dark/40 border-vista-light/20 hover:bg-vista-blue/10 hover:border-vista-blue/30 h-9 sm:h-10 px-3 sm:px-4 text-sm font-medium transition-colors duration-200 w-full sm:w-auto">
                <Upload className="mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                <span className="truncate">Import Users</span>
              </Button>
            </div>
          </div>

          {/* Mobile Cards / Desktop Table Container */}
          <div className="rounded-md border border-vista-light/10 overflow-hidden shadow-md bg-gradient-to-b from-vista-dark/50 to-vista-dark/30">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-vista-blue/0 via-vista-blue/30 to-vista-blue/0"></div>

            {/* Mobile Card View (hidden on md and up) */}
            <div className="md:hidden">
              {/* Mobile Select All Header */}
              <div className="p-4 border-b border-vista-light/10 bg-vista-dark/60 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Checkbox
                    checked={selectAll}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all users"
                    className="h-5 w-5 border-vista-light/30 transition-colors duration-150"
                  />
                  <span className="text-vista-light/90 font-medium text-sm">
                    {selectedUsers.length > 0 ? `${selectedUsers.length} selected` : 'Select all'}
                  </span>
                </div>
                <span className="text-vista-light/70 text-sm">
                  {pagination.total} users
                </span>
              </div>

              {/* Mobile Cards */}
              <div className="divide-y divide-vista-light/5">
                {isLoading ? (
                  <div className="p-6 text-center text-vista-light/70">
                    <div className="flex flex-col items-center">
                      <div className="relative w-12 h-12 mb-4">
                        <div className="absolute inset-0 rounded-full border-4 border-t-vista-blue border-r-vista-light/20 border-b-vista-light/20 border-l-vista-light/20 animate-spin"></div>
                        <div className="absolute inset-2 rounded-full border-2 border-t-transparent border-r-vista-blue/40 border-b-vista-blue/40 border-l-transparent animate-spin animation-delay-150"></div>
                        <RefreshCw className="h-5 w-5 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-vista-light/70" />
                      </div>
                      <span className="text-sm">Loading users...</span>
                    </div>
                  </div>
                ) : error ? (
                  <div className="p-6 text-center text-destructive">
                    <div className="flex flex-col items-center">
                      <div className="relative w-10 h-10 mb-4">
                        <div className="absolute inset-0 rounded-full border-4 border-red-500/30"></div>
                        <AlertCircle className="h-5 w-5 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-red-500" />
                      </div>
                      <span className="text-sm mb-3">Error loading users: {error.message || 'Unknown error'}</span>
                      <Button variant="outline" size="sm" onClick={handleRefreshData} className="border-vista-light/30 bg-vista-dark/50 hover:bg-vista-light/10 transition-colors duration-200">
                        Try Again
                      </Button>
                    </div>
                  </div>
                ) : users.length > 0 ? (
                  users.map((user: User, index) => (
                    <div
                      key={user.id}
                      className={`p-4 hover:bg-vista-dark/50 transition-all duration-200 cursor-pointer ${
                        index % 2 === 0 ? 'bg-vista-dark/10' : 'bg-vista-dark/20'
                      }`}
                      onClick={() => router.push(`/admin/users/${user.id}`)}
                    >
                      <div className="flex items-start gap-3">
                        {/* Checkbox and Avatar */}
                        <div className="flex items-center gap-3 flex-shrink-0">
                          <Checkbox
                            checked={selectedUsers.includes(user.id)}
                            onCheckedChange={(checked: boolean | 'indeterminate') => {
                              handleUserSelection(user.id, !!checked);
                            }}
                            onClick={(e) => e.stopPropagation()}
                            aria-label={`Select ${user.name}`}
                            className="h-5 w-5 border-vista-light/30 transition-colors duration-150"
                          />
                          <div className="relative">
                            <UserAvatar
                              userId={user.id}
                              src={user.profileImage || user.picture}
                              alt={user.name}
                              fallback={user.name.charAt(0).toUpperCase()}
                              className="h-12 w-12 border border-vista-light/10 shadow-sm"
                            />
                            {user.verified && (
                              <div className="absolute -bottom-1 -right-1 bg-green-500 rounded-full p-0.5 shadow-md">
                                <CheckCircle className="h-3 w-3 text-black" />
                              </div>
                            )}
                          </div>
                        </div>

                        {/* User Info */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-2">
                            <div className="min-w-0 flex-1">
                              <h3 className="font-medium text-vista-light text-base truncate">
                                {user.name}
                              </h3>
                              <p className="text-vista-light/70 text-sm truncate">
                                {user.email}
                              </p>
                            </div>
                            <div className="ml-2 flex-shrink-0" onClick={(e) => e.stopPropagation()}>
                              <ActionDropdown user={user} />
                            </div>
                          </div>

                          {/* Badges Row */}
                          <div className="flex items-center gap-2 mb-3">
                            <Badge
                              variant={
                                user.role === 'admin'
                                  ? 'success'
                                  : user.role === 'superadmin'
                                    ? 'destructive'
                                    : 'outline'
                              }
                              className={`text-xs py-1 px-2 font-medium ${
                                user.role === 'admin'
                                  ? 'bg-green-500/30 text-green-200 border-green-400/30'
                                  : user.role === 'superadmin'
                                    ? 'bg-red-500/30 text-red-200 border-red-400/30'
                                    : 'bg-vista-blue/20 text-vista-light border-vista-blue/30'
                              }`}
                            >
                              {user.role}
                            </Badge>
                            <Badge variant="outline" className="text-xs py-1 px-2 border-vista-light/30 bg-vista-light/10 text-vista-light">
                              {typeof user.subscription === 'string'
                                ? user.subscription
                                : user.subscription && typeof user.subscription === 'object' && 'plan' in user.subscription
                                  ? user.subscription.plan
                                  : 'Free Plan'}
                            </Badge>
                          </div>

                          {/* Dates Row */}
                          <div className="grid grid-cols-2 gap-4 text-xs text-vista-light/70">
                            <div>
                              <span className="block font-medium text-vista-light/90">Created</span>
                              <span>{formatDate(user.createdAt)}</span>
                            </div>
                            <div>
                              <span className="block font-medium text-vista-light/90">Last Login</span>
                              <span>{formatDate(user.lastLogin)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-6 text-center text-vista-light/70">
                    <div className="flex flex-col items-center">
                      <div className="relative w-10 h-10 mb-4 opacity-60">
                        <Users className="h-8 w-8 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-vista-light/50" />
                        <div className="absolute inset-0 border-2 border-dashed border-vista-light/10 rounded-full animate-pulse"></div>
                      </div>
                      <span className="text-sm">No users found matching your search criteria.</span>
                      <span className="text-xs text-vista-light/50 mt-1">Try adjusting your filters</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Desktop Table View (hidden on mobile) */}
            <div className="hidden md:block overflow-x-auto">
              <Table className="min-w-[800px]">
                <TableHeader>
                  <TableRow className="border-b border-vista-light/10 bg-vista-dark/60">
                    <TableHead className="w-[40px] sm:w-[50px] py-2.5 sm:py-3.5">
                      <Checkbox
                        checked={selectAll}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all users"
                        className="h-4 w-4 sm:h-5 sm:w-5 border-vista-light/30 transition-colors duration-150"
                      />
                    </TableHead>
                    <TableHead className="w-[180px] sm:w-[200px] py-2.5 sm:py-3.5 text-vista-light/90 font-medium text-sm sm:text-base">User</TableHead>
                    <TableHead className="w-[100px] sm:w-[120px] py-2.5 sm:py-3.5 text-vista-light/90 font-medium text-sm sm:text-base">Role</TableHead>
                    <TableHead className="w-[120px] sm:w-[150px] py-2.5 sm:py-3.5 text-vista-light/90 font-medium text-sm sm:text-base">Subscription</TableHead>
                    <TableHead className="w-[120px] sm:w-[150px] py-2.5 sm:py-3.5 text-vista-light/90 font-medium text-sm sm:text-base">Created</TableHead>
                    <TableHead className="w-[120px] sm:w-[150px] py-2.5 sm:py-3.5 text-vista-light/90 font-medium text-sm sm:text-base">Last Login</TableHead>
                    <TableHead className="w-[80px] sm:w-[100px] py-2.5 sm:py-3.5 text-right text-vista-light/90 font-medium text-sm sm:text-base">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="divide-y divide-vista-light/5">
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12 sm:py-16 text-vista-light/70">
                      <div className="flex flex-col items-center">
                        <div className="relative w-12 h-12 sm:w-14 sm:h-14 mb-4">
                          <div className="absolute inset-0 rounded-full border-4 border-t-vista-blue border-r-vista-light/20 border-b-vista-light/20 border-l-vista-light/20 animate-spin"></div>
                          <div className="absolute inset-2 rounded-full border-2 border-t-transparent border-r-vista-blue/40 border-b-vista-blue/40 border-l-transparent animate-spin animation-delay-150"></div>
                          <RefreshCw className="h-5 w-5 sm:h-6 sm:w-6 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-vista-light/70" />
                        </div>
                        <span className="text-sm sm:text-base">Loading users...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12 sm:py-16 text-destructive">
                      <div className="flex flex-col items-center">
                        <div className="relative w-10 h-10 sm:w-12 sm:h-12 mb-4">
                          <div className="absolute inset-0 rounded-full border-4 border-red-500/30"></div>
                          <AlertCircle className="h-5 w-5 sm:h-6 sm:w-6 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-red-500" />
                        </div>
                        <span className="text-sm sm:text-base mb-3">Error loading users: {error.message || 'Unknown error'}</span>
                        <Button variant="outline" size="sm" onClick={handleRefreshData} className="border-vista-light/30 bg-vista-dark/50 hover:bg-vista-light/10 transition-colors duration-200">
                          Try Again
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : users.length > 0 ? (
                  users.map((user: User, index) => (
                    <TableRow
                      key={user.id}
                      isSelectable
                      className={`
                        hover:bg-vista-dark/50 hover:backdrop-blur-sm transition-all duration-200
                        border-b border-vista-light/5 group
                        ${index % 2 === 0 ? 'bg-vista-dark/10' : 'bg-vista-dark/20'}
                      `}
                    >
                      <TableCell className="py-3 sm:py-4 pl-3 sm:pl-4">
                        <Checkbox
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={(checked: boolean | 'indeterminate') => handleUserSelection(user.id, !!checked)}
                          aria-label={`Select ${user.name}`}
                          className="h-4 w-4 sm:h-5 sm:w-5 border-vista-light/30 transition-colors duration-150"
                        />
                      </TableCell>
                      <TableCell className="py-3 sm:py-4">
                        <div className="flex items-center gap-2 sm:gap-4">
                          <div className="relative group">
                            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-vista-blue/0 to-vista-blue/0 group-hover:from-vista-blue/20 group-hover:to-vista-blue/5 transition-all duration-300 -m-1 scale-0 group-hover:scale-110 opacity-0 group-hover:opacity-100"></div>
                            <UserAvatar
                              userId={user.id}
                              src={user.profileImage || user.picture}
                              alt={user.name}
                              fallback={user.name.charAt(0).toUpperCase()}
                              className="h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12 flex-shrink-0 border border-vista-light/10 shadow-sm z-10 relative transition-transform duration-300 group-hover:scale-105 ring-2 ring-transparent group-hover:ring-vista-blue/20"
                            />
                            {user.verified && (
                              <div className="absolute -bottom-0.5 -right-0.5 sm:-bottom-1 sm:-right-1 bg-green-500 rounded-full p-0.5 shadow-md z-20">
                                <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-black" />
                              </div>
                            )}
                          </div>
                          <div className="min-w-0 flex-1">
                            <Button
                              variant="link"
                              className="p-0 h-auto font-medium text-vista-light text-sm sm:text-base hover:text-vista-blue focus-visible:ring-offset-0 focus-visible:ring-0 transition-colors duration-200 text-left justify-start"
                              onClick={() => router.push(`/admin/users/${user.id}`)}
                            >
                              <span className="truncate">{user.name}</span>
                            </Button>
                            <p className="text-vista-light/70 text-xs sm:text-sm mt-0.5 sm:mt-1 truncate">{user.email}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-3 sm:py-4">
                        <Badge
                          variant={
                            user.role === 'admin'
                              ? 'success'
                              : user.role === 'superadmin'
                                ? 'destructive'
                                : 'outline'
                          }
                          className={`capitalize text-xs sm:text-sm py-0.5 sm:py-1 px-2 sm:px-3 font-medium transition-all duration-200 ${
                            user.role === 'admin'
                              ? 'bg-green-500/30 hover:bg-green-500/40 text-green-200 border-green-400/30 hover:shadow-green-900/20 hover:shadow-inner'
                              : user.role === 'superadmin'
                                ? 'bg-red-500/30 hover:bg-red-500/40 text-red-200 border-red-400/30 hover:shadow-red-900/20 hover:shadow-inner'
                                : 'bg-vista-blue/20 hover:bg-vista-blue/30 text-vista-light border-vista-blue/30 hover:shadow-vista-blue/20 hover:shadow-inner'
                          }`}>
                          <span className="w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full mr-1 sm:mr-1.5 inline-block bg-current opacity-70"></span>
                          <span className="truncate">{user.role}</span>
                        </Badge>
                      </TableCell>
                      <TableCell className="py-3 sm:py-4">
                        <Badge variant="outline" className="capitalize text-xs sm:text-sm py-0.5 sm:py-1 px-2 sm:px-3 border-vista-light/30 bg-vista-light/10 text-vista-light hover:bg-vista-light/15 font-medium transition-colors duration-200">
                          <span className="truncate">
                            {typeof user.subscription === 'string'
                              ? user.subscription
                              : user.subscription && typeof user.subscription === 'object' && 'plan' in user.subscription
                                ? user.subscription.plan
                                : 'Free Plan'}
                          </span>
                        </Badge>
                      </TableCell>
                      <TableCell className="py-3 sm:py-4 text-vista-light/90 text-xs sm:text-sm md:text-base">
                        <span className="truncate block">{formatDate(user.createdAt)}</span>
                      </TableCell>
                      <TableCell className="py-3 sm:py-4 text-vista-light/90 text-xs sm:text-sm md:text-base">
                        <span className="truncate block">{formatDate(user.lastLogin)}</span>
                      </TableCell>
                      <TableCell className="text-right py-3 sm:py-4 pr-3 sm:pr-4">
                        <ActionDropdown user={user} />
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12 sm:py-16 text-vista-light/70">
                      <div className="flex flex-col items-center">
                        <div className="relative w-10 h-10 sm:w-12 sm:h-12 mb-4 opacity-60">
                          <Users className="h-8 w-8 sm:h-10 sm:w-10 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-vista-light/50" />
                          <div className="absolute inset-0 border-2 border-dashed border-vista-light/10 rounded-full animate-pulse"></div>
                        </div>
                        <span className="text-sm sm:text-base">No users found matching your search criteria.</span>
                        <span className="text-xs sm:text-sm text-vista-light/50 mt-1">Try adjusting your filters</span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            </div>
          </div>

          {/* Pagination */}
          {pagination.pages > 0 && (
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.pages}
              onPageChange={handlePageChange}
              isLoading={isLoading}
              totalItems={pagination.total}
              itemsPerPage={pagination.limit}
            />
          )}
        </CardContent>
      </Card>

      {/* User Form Modal */}
      <UserFormModal
        isOpen={isUserModalOpen}
        onClose={handleModalClose}
        onSuccess={refetch}
        editUser={editingUser}
      />

      {/* Reset Password Modal */}
      {selectedUserForPasswordReset && (
        <ResetPasswordModal
          isOpen={isResetPasswordModalOpen}
          onClose={() => {
            setIsResetPasswordModalOpen(false);
            setSelectedUserForPasswordReset(null);
          }}
          userId={selectedUserForPasswordReset.id}
          userName={selectedUserForPasswordReset.name}
          onSuccess={() => {
            setIsResetPasswordModalOpen(false);
            setSelectedUserForPasswordReset(null);
            toast({
              title: 'Password Reset',
              description: `Password for ${selectedUserForPasswordReset.name} has been reset successfully.`,
            });
          }}
        />
      )}

      {/* Import Users Modal */}
      <ImportUsersModal
        isOpen={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
        onSuccess={refetch}
      />

      {/* Delete User Dialog */}
      <DeleteUserDialog
        open={!!userToDelete}
        onOpenChange={(open) => {
          if (!open) {
            setUserToDelete(null);
          }
        }}
        userId={userToDelete?.id || ''}
        userName={userToDelete?.name || ''}
        userEmail={userToDelete?.email}
        userRole={userToDelete?.role}
        userImage={userToDelete?.profileImage || userToDelete?.picture}
        onSuccess={handleDeleteConfirmed}
      />
    </div>
  );
}
