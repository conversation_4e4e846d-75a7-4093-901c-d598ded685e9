'use client';

import { useMemo, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Search, Check } from 'lucide-react';

interface FilterOption {
  id: string;
  name: string;
}

// Add sort options
interface SortOption {
  id: string; // e.g., 'popularity.desc'
  name: string; // e.g., 'Popularity Descending'
}

interface FilterPanelProps {
  availableGenres: string[];
  yearFilters: FilterOption[];
  ratingFilters: FilterOption[];
  sortOptions: SortOption[]; // Add sort options
  selectedGenres: string[];
  selectedYear: string;
  selectedRating: string;
  selectedSortBy: string; // Add selected sort
  onGenreToggle: (genre: string) => void;
  onYearChange: (yearId: string) => void;
  onRatingChange: (ratingId: string) => void;
  onSortChange: (sortId: string) => void; // Add sort handler
  filterType: 'movie' | 'show';
}

export function FilterPanel({
  availableGenres,
  yearFilters,
  ratingFilters,
  sortOptions, // Destructure new props
  selectedGenres,
  selectedYear,
  selectedRating,
  selectedSortBy,
  onGenreToggle,
  onYearChange,
  onRatingChange,
  onSortChange,
  filterType = 'movie',
}: FilterPanelProps) {

  const yearLabel = filterType === 'show' ? 'First Air Year' : 'Release Year';

  // Local state for a cleaner, searchable genre list
  const [genreQuery, setGenreQuery] = useState('');
  const filteredGenres = useMemo(() => {
    if (!genreQuery.trim()) return availableGenres;
    const q = genreQuery.toLowerCase();
    return availableGenres.filter(g => g.toLowerCase().includes(q));
  }, [availableGenres, genreQuery]);

  return (
    <Card className="w-full bg-vista-dark-lighter/90 border-vista-light/10 rounded-2xl">
      <CardContent className="p-5 md:p-6">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 md:gap-8">
          {/* Genres */}
          <div className="lg:col-span-8 space-y-3">
            <h3 className="text-xs font-semibold uppercase tracking-wide text-vista-light/70">Genres</h3>
            <div className="relative">
              <Input
                value={genreQuery}
                onChange={(e) => setGenreQuery(e.target.value)}
                placeholder="Search genres"
                className="pl-9 h-9 text-xs bg-vista-dark border-vista-light/20 text-vista-light placeholder:text-vista-light/40"
              />
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-vista-light/40" />
            </div>
            <div className="flex flex-wrap gap-2">
              {filteredGenres.map((genre) => {
                const selected = selectedGenres.includes(genre);
                return (
                  <Badge
                    key={genre}
                    variant={selected ? 'default' : 'outline'}
                    aria-pressed={selected}
                    className={`cursor-pointer select-none transition-colors text-xs px-3 py-1 rounded-full border ${
                      selected
                        ? 'bg-vista-blue/15 text-vista-blue border-vista-blue/40 hover:bg-vista-blue/20'
                        : 'border-vista-light/15 bg-transparent text-vista-light/70 hover:border-vista-light/30 hover:bg-vista-light/5'
                    }`}
                    onClick={() => onGenreToggle(genre)}
                  >
                    {selected && <Check className="h-3.5 w-3.5 mr-1" />}
                    {genre}
                  </Badge>
                );
              })}
              {filteredGenres.length === 0 && (
                <span className="text-xs text-vista-light/50">No genres match “{genreQuery}”.</span>
              )}
            </div>
          </div>

          {/* Year */}
          <div className="lg:col-span-4 space-y-3">
            <h3 className="text-xs font-semibold uppercase tracking-wide text-vista-light/70">{yearLabel}</h3>
            <Select value={selectedYear} onValueChange={onYearChange}>
              <SelectTrigger className="w-full bg-vista-dark border-vista-light/20 text-vista-light focus:ring-vista-blue text-xs h-9">
                <SelectValue placeholder={`Select ${yearLabel}`} />
              </SelectTrigger>
              <SelectContent className="bg-vista-dark border-vista-light/20 text-vista-light">
                {yearFilters.map((option) => (
                  <SelectItem key={option.id} value={option.id} className="focus:bg-vista-light/10 text-xs">
                    {option.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Rating */}
          <div className="lg:col-span-4 space-y-3">
            <h3 className="text-xs font-semibold uppercase tracking-wide text-vista-light/70">Rating</h3>
            <Select value={selectedRating} onValueChange={onRatingChange}>
              <SelectTrigger className="w-full bg-vista-dark border-vista-light/20 text-vista-light focus:ring-vista-blue text-xs h-9">
                <SelectValue placeholder="Select Rating" />
              </SelectTrigger>
              <SelectContent className="bg-vista-dark border-vista-light/20 text-vista-light">
                {ratingFilters.map((option) => (
                  <SelectItem key={option.id} value={option.id} className="focus:bg-vista-light/10 text-xs">
                    {option.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Sort By */}
          <div className="lg:col-span-4 space-y-3">
            <h3 className="text-xs font-semibold uppercase tracking-wide text-vista-light/70">Sort By</h3>
            <Select value={selectedSortBy} onValueChange={onSortChange}>
              <SelectTrigger className="w-full bg-vista-dark border-vista-light/20 text-vista-light focus:ring-vista-blue text-xs h-9">
                <SelectValue placeholder="Select sorting" />
              </SelectTrigger>
              <SelectContent className="bg-vista-dark border-vista-light/20 text-vista-light">
                {sortOptions.map((option) => (
                  <SelectItem key={option.id} value={option.id} className="focus:bg-vista-light/10 text-xs">
                    {option.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 