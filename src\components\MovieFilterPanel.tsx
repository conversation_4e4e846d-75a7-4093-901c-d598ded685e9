import React, { useState, useRef, useEffect } from 'react';
import { Search, ChevronDown, SlidersHorizontal, X } from 'lucide-react';
import { cn } from '@/lib/utils';

type GenreType = {
  id: string;
  name: string;
};

const genres = [
  { id: '1', name: 'Action' },
  { id: '2', name: 'Adventure' },
  { id: '3', name: 'Animation' },
  { id: '4', name: 'Comedy' },
  { id: '5', name: 'Crime' },
  { id: '6', name: 'Documentary' },
  { id: '7', name: 'Drama' },
  { id: '8', name: 'Family' },
  { id: '9', name: 'Fantasy' },
  { id: '10', name: 'History' },
  { id: '11', name: 'Horror' },
  { id: '12', name: 'Music' },
  { id: '13', name: 'Mystery' },
  { id: '14', name: 'Romance' },
  { id: '15', name: 'Science Fiction' },
  { id: '16', name: 'TV Movie' },
  { id: '17', name: 'Thriller' },
  { id: '18', name: 'War' },
  { id: '19', name: 'Western' }
];

const years = [
  'All Years',
  '2023',
  '2022',
  '2021',
  '2020',
  '2019',
  '2018',
  '2010-2017',
  '2000-2009',
  '1990-1999',
  'Before 1990'
];

const ratings = [
  'All Ratings',
  '9+',
  '8+',
  '7+',
  '6+',
  '5+',
  '4+',
  '3+',
  '2+',
  '1+'
];

const sortOptions = [
  'Popularity Descending',
  'Popularity Ascending',
  'Rating Descending',
  'Rating Ascending',
  'Release Date Descending',
  'Release Date Ascending',
  'Title A-Z',
  'Title Z-A'
];

interface MovieFilterPanelProps {
  onFilterChange: (filters: any) => void;
}

const MovieFilterPanel: React.FC<MovieFilterPanelProps> = ({ onFilterChange }) => {
  const [selectedGenres, setSelectedGenres] = useState<string[]>([]);
  const [genreSearch, setGenreSearch] = useState('');
  const [selectedYear, setSelectedYear] = useState(years[0]);
  const [selectedRating, setSelectedRating] = useState(ratings[0]);
  const [selectedSort, setSelectedSort] = useState(sortOptions[0]);
  const [isYearDropdownOpen, setIsYearDropdownOpen] = useState(false);
  const [isRatingDropdownOpen, setIsRatingDropdownOpen] = useState(false);
  const [isSortDropdownOpen, setIsSortDropdownOpen] = useState(false);
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);

  const yearRef = useRef<HTMLDivElement>(null);
  const ratingRef = useRef<HTMLDivElement>(null);
  const sortRef = useRef<HTMLDivElement>(null);

  const filteredGenres = genres.filter(genre => 
    genre.name.toLowerCase().includes(genreSearch.toLowerCase())
  );

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (yearRef.current && !yearRef.current.contains(event.target as Node)) {
        setIsYearDropdownOpen(false);
      }
      if (ratingRef.current && !ratingRef.current.contains(event.target as Node)) {
        setIsRatingDropdownOpen(false);
      }
      if (sortRef.current && !sortRef.current.contains(event.target as Node)) {
        setIsSortDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    onFilterChange({
      genres: selectedGenres,
      year: selectedYear,
      rating: selectedRating,
      sort: selectedSort
    });
  }, [selectedGenres, selectedYear, selectedRating, selectedSort, onFilterChange]);

  const toggleGenre = (genreName: string) => {
    setSelectedGenres(prev => 
      prev.includes(genreName) 
        ? prev.filter(g => g !== genreName) 
        : [...prev, genreName]
    );
  };

  const handleClearFilters = () => {
    setSelectedGenres([]);
    setSelectedYear(years[0]);
    setSelectedRating(ratings[0]);
    setSelectedSort(sortOptions[0]);
  };

  return (
    <div className="w-full bg-gray-900 rounded-lg overflow-hidden shadow-lg">
      <div className="p-4 flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <h3 className="text-white font-medium text-lg">Filters</h3>
          <div className="flex items-center gap-2">
            <button 
              onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
              className="flex items-center gap-1.5 bg-gray-800 hover:bg-gray-700 transition-colors px-3 py-1.5 rounded-md text-gray-200 text-sm"
            >
              <SlidersHorizontal size={16} />
              {isAdvancedOpen ? 'Simple Filter' : 'Advanced Filter'}
            </button>
            {(selectedGenres.length > 0 || selectedYear !== years[0] || selectedRating !== ratings[0] || selectedSort !== sortOptions[0]) && (
              <button 
                onClick={handleClearFilters}
                className="flex items-center gap-1.5 bg-blue-600 hover:bg-blue-700 transition-colors px-3 py-1.5 rounded-md text-white text-sm"
              >
                <X size={16} />
                Clear All
              </button>
            )}
          </div>
        </div>

        {/* Genres Section */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-gray-400 text-sm uppercase font-medium tracking-wide">Genres</h4>
            <div className="relative w-1/3">
              <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-500" size={16} />
              <input
                type="text"
                placeholder="Search genres"
                value={genreSearch}
                onChange={(e) => setGenreSearch(e.target.value)}
                className="w-full bg-gray-800 text-gray-200 pl-8 pr-3 py-1.5 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            {filteredGenres.map(genre => (
              <button
                key={genre.id}
                onClick={() => toggleGenre(genre.name)}
                className={cn(
                  "py-1.5 px-3 rounded-full text-sm transition-all",
                  selectedGenres.includes(genre.name)
                    ? "bg-blue-600 text-white"
                    : "bg-gray-800 text-gray-300 hover:bg-gray-700"
                )}
              >
                {genre.name}
              </button>
            ))}
            {filteredGenres.length === 0 && (
              <p className="text-gray-500 text-sm">No genres match your search</p>
            )}
          </div>
        </div>

        {/* Advanced Filters (Year, Rating, Sort) */}
        <div className={cn("grid gap-4", isAdvancedOpen ? "grid-cols-1 md:grid-cols-3" : "hidden")}>
          {/* Year Filter */}
          <div ref={yearRef} className="relative">
            <h4 className="text-gray-400 text-sm uppercase font-medium tracking-wide mb-2">Release Year</h4>
            <button
              onClick={() => {
                setIsYearDropdownOpen(!isYearDropdownOpen);
                setIsRatingDropdownOpen(false);
                setIsSortDropdownOpen(false);
              }}
              className="w-full flex items-center justify-between bg-gray-800 hover:bg-gray-700 transition-colors px-3 py-2 rounded-md text-gray-200"
            >
              <span>{selectedYear}</span>
              <ChevronDown 
                size={18} 
                className={cn(
                  "transition-transform duration-200",
                  isYearDropdownOpen ? "transform rotate-180" : ""
                )} 
              />
            </button>
            {isYearDropdownOpen && (
              <div className="absolute z-10 mt-1 w-full bg-gray-800 border border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto">
                {years.map(year => (
                  <button
                    key={year}
                    onClick={() => {
                      setSelectedYear(year);
                      setIsYearDropdownOpen(false);
                    }}
                    className={cn(
                      "w-full text-left px-4 py-2 text-sm hover:bg-gray-700",
                      year === selectedYear ? "bg-blue-600 text-white" : "text-gray-200"
                    )}
                  >
                    {year}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Rating Filter */}
          <div ref={ratingRef} className="relative">
            <h4 className="text-gray-400 text-sm uppercase font-medium tracking-wide mb-2">Rating</h4>
            <button
              onClick={() => {
                setIsRatingDropdownOpen(!isRatingDropdownOpen);
                setIsYearDropdownOpen(false);
                setIsSortDropdownOpen(false);
              }}
              className="w-full flex items-center justify-between bg-gray-800 hover:bg-gray-700 transition-colors px-3 py-2 rounded-md text-gray-200"
            >
              <span>{selectedRating}</span>
              <ChevronDown 
                size={18} 
                className={cn(
                  "transition-transform duration-200",
                  isRatingDropdownOpen ? "transform rotate-180" : ""
                )} 
              />
            </button>
            {isRatingDropdownOpen && (
              <div className="absolute z-10 mt-1 w-full bg-gray-800 border border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto">
                {ratings.map(rating => (
                  <button
                    key={rating}
                    onClick={() => {
                      setSelectedRating(rating);
                      setIsRatingDropdownOpen(false);
                    }}
                    className={cn(
                      "w-full text-left px-4 py-2 text-sm hover:bg-gray-700",
                      rating === selectedRating ? "bg-blue-600 text-white" : "text-gray-200"
                    )}
                  >
                    {rating}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Sort By */}
          <div ref={sortRef} className="relative">
            <h4 className="text-gray-400 text-sm uppercase font-medium tracking-wide mb-2">Sort By</h4>
            <button
              onClick={() => {
                setIsSortDropdownOpen(!isSortDropdownOpen);
                setIsYearDropdownOpen(false);
                setIsRatingDropdownOpen(false);
              }}
              className="w-full flex items-center justify-between bg-gray-800 hover:bg-gray-700 transition-colors px-3 py-2 rounded-md text-gray-200"
            >
              <span>{selectedSort}</span>
              <ChevronDown 
                size={18} 
                className={cn(
                  "transition-transform duration-200",
                  isSortDropdownOpen ? "transform rotate-180" : ""
                )} 
              />
            </button>
            {isSortDropdownOpen && (
              <div className="absolute z-10 mt-1 w-full bg-gray-800 border border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto">
                {sortOptions.map(option => (
                  <button
                    key={option}
                    onClick={() => {
                      setSelectedSort(option);
                      setIsSortDropdownOpen(false);
                    }}
                    className={cn(
                      "w-full text-left px-4 py-2 text-sm hover:bg-gray-700",
                      option === selectedSort ? "bg-blue-600 text-white" : "text-gray-200"
                    )}
                  >
                    {option}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Active Filters Display */}
        {selectedGenres.length > 0 && (
          <div>
            <h4 className="text-gray-400 text-sm uppercase font-medium tracking-wide mb-2">Active Filters</h4>
            <div className="flex flex-wrap gap-2">
              {selectedGenres.map(genre => (
                <div key={genre} className="bg-blue-600/20 border border-blue-600/30 text-blue-400 px-2 py-1 rounded-md text-xs flex items-center gap-1">
                  <span>{genre}</span>
                  <button onClick={() => toggleGenre(genre)} className="hover:text-white">
                    <X size={14} />
                  </button>
                </div>
              ))}
              {selectedYear !== years[0] && (
                <div className="bg-blue-600/20 border border-blue-600/30 text-blue-400 px-2 py-1 rounded-md text-xs flex items-center gap-1">
                  <span>Year: {selectedYear}</span>
                  <button onClick={() => setSelectedYear(years[0])} className="hover:text-white">
                    <X size={14} />
                  </button>
                </div>
              )}
              {selectedRating !== ratings[0] && (
                <div className="bg-blue-600/20 border border-blue-600/30 text-blue-400 px-2 py-1 rounded-md text-xs flex items-center gap-1">
                  <span>Rating: {selectedRating}</span>
                  <button onClick={() => setSelectedRating(ratings[0])} className="hover:text-white">
                    <X size={14} />
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MovieFilterPanel;

